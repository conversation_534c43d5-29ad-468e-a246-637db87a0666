html,
body,
#app {
    height: 100%;
    margin: 0;
    padding: 0;
}

#loader-wrapper {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 99999;
    background: linear-gradient(135deg, #f5f7fa 0%, #e4e8ed 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    overflow: hidden;
}

#loader-wrapper::before {
    content: '';
    position: absolute;
    width: 200%;
    height: 200%;
    top: -50%;
    left: -50%;
    background: radial-gradient(circle, transparent 20%, #e4e8ed 80%);
    background-size: 20px 20px;
    animation: backgroundMove 10s linear infinite;
    opacity: 0.3;
    z-index: -1;
}

@keyframes backgroundMove {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

#loader-wrapper .loader-box {
    position: relative;
    perspective: 800px;
    transform-style: preserve-3d;
    width: 300px;
    height: 100px;
    display: flex;
    justify-content: center;
    align-items: center;
}

#loader-wrapper .loader-box > span {
    display: inline-block;
    font-size: 42px;
    font-weight: bold;
    margin: 0 5px;
    color: transparent;
    position: relative;
    background: linear-gradient(92deg, #2c5aaa 0%, #3e7bd6 100%);
    -webkit-background-clip: text;
    background-clip: text;
    text-shadow: 0 0 8px rgba(60, 100, 170, 0.3);
    transform-style: preserve-3d;
    animation: char-animation 3s infinite;
}

#loader-wrapper .loader-box > span::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 4px;
    background: linear-gradient(92deg, #2c5aaa 0%, #3e7bd6 100%);
    bottom: -8px;
    left: 0;
    border-radius: 50%;
    filter: blur(3px);
    opacity: 0;
    animation: shadow-animation 3s infinite;
}

@keyframes shadow-animation {
    0%, 100% {
        opacity: 0;
        transform: scaleX(0.8);
    }
    25% {
        opacity: 0.8;
        transform: scaleX(1.2);
    }
    50% {
        opacity: 0.2;
        transform: scaleX(1);
    }
    75% {
        opacity: 0.6;
        transform: scaleX(0.8);
    }
}

#loader-wrapper .loader-box > span:nth-child(1) {
    animation-delay: 0s;
}

#loader-wrapper .loader-box > span:nth-child(2) {
    animation-delay: 0.2s;
}

#loader-wrapper .loader-box > span:nth-child(3) {
    animation-delay: 0.4s;
}

#loader-wrapper .loader-box > span:nth-child(4) {
    animation-delay: 0.6s;
}

#loader-wrapper .loader-box > span:nth-child(5) {
    animation-delay: 0.8s;
}

@keyframes char-animation {
    0%, 100% {
        transform: translateY(0) rotateX(0) scale(1);
        filter: brightness(1);
    }
    25% {
        transform: translateY(-25px) rotateX(20deg) scale(1.2);
        filter: brightness(1.5);
    }
    50% {
        transform: translateY(0) rotateX(0) scale(1);
        filter: brightness(1);
    }
    75% {
        transform: translateY(15px) rotateX(-20deg) scale(0.8);
        filter: brightness(0.8);
    }
}

/* 添加加载进度指示器 */
#loader-wrapper .progress-container {
    position: relative;
    width: 200px;
    height: 4px;
    background-color: rgba(44, 90, 170, 0.1);
    border-radius: 10px;
    margin-top: 30px;
    overflow: hidden;
}

#loader-wrapper .progress-bar {
    position: absolute;
    height: 100%;
    background: linear-gradient(92deg, #2c5aaa 0%, #3e7bd6 100%);
    border-radius: 10px;
    width: 0;
    animation: progress 3s ease-in-out infinite;
}

@keyframes progress {
    0% {
        width: 0;
        opacity: 0.8;
    }
    20% {
        width: 20%;
        opacity: 1;
    }
    40% {
        width: 40%;
        opacity: 0.8;
    }
    60% {
        width: 60%;
        opacity: 1;
    }
    80% {
        width: 80%;
        opacity: 0.8;
    }
    100% {
        width: 100%;
        opacity: 1;
    }
}

#loader-wrapper .loader-title {
    margin-top: 20px;
    font-size: 18px;
    color: #606778;
    position: relative;
    letter-spacing: 2px;
    text-align: center;
    animation: pulse 2s ease-in-out infinite alternate;
}

@keyframes pulse {
    0% {
        opacity: 0.5;
        text-shadow: 0 0 3px rgba(96, 103, 120, 0.1);
        transform: scale(0.98);
    }
    100% {
        opacity: 1;
        text-shadow: 0 0 8px rgba(96, 103, 120, 0.3);
        transform: scale(1.02);
    }
}

/* 添加粒子效果 */
#loader-wrapper::after {
    content: '';
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle, #2c5aaa 1px, transparent 1px),
        radial-gradient(circle, #3e7bd6 1px, transparent 1px);
    background-size: 40px 40px;
    background-position: 0 0, 20px 20px;
    opacity: 0.05;
    animation: particles 12s linear infinite;
}

@keyframes particles {
    0% {
        background-position: 0 0, 20px 20px;
    }
    100% {
        background-position: 40px 40px, 60px 60px;
    }
}
